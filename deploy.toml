[environments.test]
server = "test-server:22"
username = "test-user"
password = "test-password"
java_path = "/usr/bin/java"
remote_base_path = "/opt/test/apps"
jar_files = [
    "admin.jar",
    "client.jar",
    "websocket.jar",
]
scripts = "prod:test"
output_dir = "dist-test"
upload_only = false

[environments.prod]
server = "prod-server:22"
username = "prod-user"
password = "prod-password"
java_path = "/usr/java/latest/bin/java"
remote_base_path = "/opt/prod/apps"
jar_files = [
    "admin.jar",
    "client.jar",
    "websocket.jar",
]
scripts = "prod"
output_dir = "dist"
upload_only = false

[environments.dev]
server = "*************:22"
username = "root"
password = "lykj"
java_path = "/opt/soft/zulu11/bin/java"
remote_base_path = "/opt/xinxuan1v1"
jar_files = [
    "admin.jar",
    "client.jar",
    "websocket.jar",
]
scripts = "prod:test"
output_dir = "dist-test"
upload_only = false

# 单模块项目示例
[environments.single]
server = "single-server:22"
username = "single-user"
password = "single-password"
java_path = "/usr/bin/java"
remote_base_path = "/opt/single/app"
jar_files = "application.jar"
scripts = "prod"
output_dir = "dist"
upload_only = false

# 仅上传示例环境
[environments.upload-only-example]
server = "upload-server:22"
username = "upload-user"
password = "upload-password"
java_path = "/usr/bin/java"
remote_base_path = "/opt/upload/apps"
jar_files = [
    "admin.jar",
    "client.jar",
]
scripts = "prod"
output_dir = "dist"
upload_only = true
