# 项目部署脚本

- 支持将编译后的程序放到springboot项目目录下，再执行命令,会从当前项目目录下读取配置文件，并根据配置文件中的环境变量，将对应的jar包上传到远程服务器，并运行jar包

- 支持将vue项目编译打包压缩zip发送到服务器,并自动解压部署

## 编译

```bash
cargo build --release
```

## windows交叉编译
```bash
brew install mingw-w64
rustup target add x86_64-pc-windows-gnu
cargo build --release --target x86_64-pc-windows-msvc
```

## 交叉编译linux程序产物
```bash
docker pull rust:latest
docker run --rm -v /c/Users/<USER>/Desktop/temp/rust/java-deploy-tool:/opt -w /opt rust:latest bash -c "rustup target add x86_64-unknown-linux-gnu && cargo build --release --target x86_64-unknown-linux-gnu"
```

## 创建配置文件

```bash
deploy-tool --init-config
```

- 配置环境说明
```toml
[environments.test]
# 远程服务器地址和端口
server = "test-server:22"
# 远程服务器用户名
username = "test-user"
# 远程服务器密码
password = "test-password"
# 远程服务器java程序路径
java_path = "/usr/bin/java"
# 远程服务器jar包部署的目录路径
remote_base_path = "/opt/test/apps"
# 当前项目中的jar包文件名,多模块项目则有多个,要保持数组类型
jar_files = [
    "admin.jar",
    "client.jar",
    "websocket.jar",
]
# npm run 后面跟随的后缀命令
scripts = "prod:test"
# 这个编译产物的输出文件夹名称
output_dir = "dist-test"


[environments.demo]
# 远程服务器地址和端口
server = "test-server:22"
# 远程服务器用户名
username = "test-user"
# 远程服务器密码
password = "test-password"
# 远程服务器java程序路径
java_path = "/usr/bin/java"
# 远程服务器jar包部署的目录路径
remote_base_path = "/opt/test/apps"
# 当前项目中的jar包文件名,如果单项目则只有一个字符串
jar_files = "admin.jar"
# npm run 后面跟随的后缀命令
scripts = "prod:test"
# 这个编译产物的输出文件夹名称
output_dir = "dist-test"
```


然后配置系统中mvn到系统path路径,不然找不到mvn命令

# vue项目多环境部署
```bash
deploy-tool -v dev,prod
```

# springboot项目多环境部署
```bash
deploy-tool -e dev,prod
```

# springboot项目多环境,多项目部署
```bash
deploy-tool -e dev,prod -m admin,client,websocket
```

# TODO: 功能目标：

## 优化方向：

1.  **更完善的错误处理和日志记录:**
    *   **详细的错误信息:**  目前的错误信息可能不够具体，例如 `ssh通信认证失败,可能密码错误`，可以更精确地指出是用户名错误还是密码错误，或者网络连接问题等。
    *   **日志记录:**  可以添加日志记录功能，将部署过程中的关键步骤和错误信息记录到日志文件中，方便用户排查问题和审计。例如，可以记录每个环境的部署开始时间、结束时间、上传的文件名、执行的命令以及任何错误信息。
    *   **日志级别控制:**  可以支持不同的日志级别（例如，debug, info, warn, error），让用户可以根据需要调整日志输出的详细程度。

2.  **更灵活的配置管理:**
    *   **支持环境变量:**  可以将一些敏感信息（例如密码）或者经常变动的信息通过环境变量来配置，而不是直接写在 `deploy.toml` 文件中，提高安全性并方便配置管理。
    *   **支持外部配置文件:**  可以支持从外部文件（例如 JSON, YAML）读取配置，方便与其他配置管理工具集成。
    *   **配置校验:**  在加载配置文件时，可以进行配置项的校验，例如检查必填项是否缺失，配置项的格式是否正确等，提前发现配置错误。

3.  **部署过程的进度显示和交互:**
    *   **进度条:**  在文件上传和远程命令执行等耗时操作时，可以添加进度条显示，让用户更直观地了解部署进度。
    *   **交互式确认:**  在执行一些敏感操作（例如，重启服务）前，可以添加交互式确认，避免误操作。

4.  **安全性增强:**
    *   **SSH 密钥认证:**  目前只支持密码认证，可以增加 SSH 密钥认证的支持，提高安全性。
    *   **敏感信息加密:**  可以将配置文件中的密码等敏感信息进行加密存储，例如使用 `secrecy` crate。

5.  **性能优化:**
    *   **并行上传:**  如果需要上传多个 JAR 包或者 Vue 项目的 ZIP 文件，可以考虑并行上传，提高上传速度。
    *   **连接复用:**  对于同一个服务器的多次操作（例如上传文件和运行命令），可以复用 SSH 连接，减少连接建立和断开的开销。

## 潜在的新功能：

1.  **部署策略选择:**
    *   **蓝绿部署:**  支持蓝绿部署策略，实现平滑升级和快速回滚。
    *   **滚动更新:**  支持滚动更新策略，逐步替换旧版本服务，减少服务中断时间。
    *   **灰度发布:**  支持灰度发布策略，先在一小部分服务器上部署新版本，验证稳定后再全量发布。

2.  **健康检查和监控:**
    *   **部署后健康检查:**  在部署完成后，可以自动执行健康检查命令（例如，访问应用的健康检查接口），验证部署是否成功。
    *   **集成监控系统:**  可以集成 Prometheus, Grafana 等监控系统，将部署状态和应用运行状态上报到监控系统，方便监控和告警。

3.  **回滚机制:**
    *   **一键回滚:**  在部署失败或者新版本出现问题时，可以提供一键回滚到上一个版本的功能。
    *   **版本管理:**  可以记录每次部署的版本信息，方便回滚和版本追溯。

4.  **支持更多项目类型:**
    *   **Docker 部署:**  支持 Docker 镜像的构建、推送和部署。
    *   **Node.js 项目部署:**  扩展支持 Node.js 项目的构建和部署。
    *   **Python 项目部署:**  扩展支持 Python 项目的部署。

5.  **图形界面或 Web 界面:**
    *   **GUI 工具:**  开发一个图形界面工具，让用户可以通过图形界面配置部署参数和执行部署操作，降低使用门槛。
    *   **Web 管理平台:**  开发一个 Web 管理平台，提供更完善的部署管理功能，例如用户管理、权限管理、部署任务管理、历史记录查看等。

6.  **插件系统:**
    *   **自定义部署步骤:**  提供插件系统，允许用户自定义部署步骤，例如在部署前后执行自定义脚本，集成第三方工具等。
    *   **扩展功能:**  通过插件扩展支持更多的项目类型、部署策略、监控系统等。

`deploy-tool` 项目还有很大的发展空间，正在通过不断优化现有功能和扩展新功能，使其成为一个更加强大、易用、可靠的自动化部署工具。
